* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background-color: #e6e8e9b2;
  min-height: 200vh;
  max-height: fit-content;
  background-size: cover;
  /* background-repeat: no-repeat; */
  background-position: center;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  padding-top: 50px; 
  /* Adjust this value if your navbar height changes */
}

header {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1100;
}

nav {
  border-radius: 3px;
  background-color: rgb(49, 48, 48);
  box-shadow: 3px 3px 5px rgb(216, 212, 212);
}

nav .logo {
  /* padding-top: 10px; */
  width: 30%;
  height: 40px;
}

nav .logo img {
  padding-left: 10px;
  width: 40%;
  height: 46px;
  object-fit: cover;
}

nav ul {
  width: 100%;
  list-style: none;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

nav li {
  height: 50px;
}

nav a {
  height: 100%;
  padding: 0 30px;
  text-decoration: none;
  display: flex;
  align-items: center;
  color: rgb(15, 15, 15);
}


nav .link{
  height: 100%;
  padding: 0 30px;
  text-decoration: none;
  display: flex;
  align-items: center;
  color: rgb(212, 210, 210);
}

nav .hideOnMobile:hover {
  color: #ffffff;
  background-color: #4b4949;
}

#login:hover {
  background-color: #f0f0f0;
}
nav li:first-child {
  margin-right: auto;
}

.sidebar {
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  width: 250px;
  background-color: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(12px);
  box-shadow: -10px 0 10px rgba(0, 0, 0, 0.1);
  list-style: none;
  display: none;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  z-index: 10010;
}

.sidebar li {
  width: 100%;
}

.sidebar a {
  width: 100%;
}

.menu-button {
  display: none;
}

@media (max-width: 800px) {
  .hideOnMobile {
    display: none;
  }
  .menu-button {
    display: block;
  }
}

@media (max-width: 400px) {
  .sidebar {
    width: 100%;
  }
}

#login {
  color: white;
  background-color: #df4213;
  border-radius: 5px;
  border-color: #df4213;
  padding: 5px 15px; /* Added some padding to make the link more appealing */
  transition: all 0.3s ease; /* Added smooth transition for responsiveness */
}

/* Centering the button */

.center-container {
  display: flex;
  justify-content: center;
  align-items: center;
  /* height: calc(100vh - 50px); Adjust height to account for the navbar */
  padding-top: 20px; /* Ensure content starts below the navbar */
}

@media (max-width: 1200px) {
  .map-button {
    padding: 8px 18px;
  }
}

@media (max-width: 800px) {
  .map-button {
    padding: 7px 16px;
  }
}

@media (max-width: 600px) {
  .map-button {
    padding: 6px 14px;
  }
}

@media (max-width: 400px) {
  .map-button {
    padding: 5px 12px;
    font-size: 14px;
  }
}

/* map  */

#map {
  height: 400px;
  position: relative;
}

.map-button {
  border-radius: 12px;
  position: absolute;
  background-color: #aeecae;
  /* top: 0px;
            margin: .5%;
            right: 46%; */
  z-index: 1000;
  padding: 5px;
  border-color: rgba(32, 27, 27, 0);
  color: rgb(61, 57, 57);
}

.map-button:hover {
  background-color: #f7f7f7;
  color: #000;
}

.map-button2 {
  /* border-radius: 12px; */
  position: absolute;
  background-color: #aeecae;
  top: 0px;
  margin: 0.5%;
  right: 76%;
  z-index: 1000;
  /* padding: 5px; */
  border-color: rgba(32, 27, 27, 0);
  color: rgb(61, 57, 57);
}

.map-button2:hover {
  background-color: #f7f7f7;
  color: #000;
}

.map-button1 {
  position: absolute;
  bottom: 10px;
  left: 10px;
  z-index: 1000;
}

img {
  margin: 0%;
  padding: 0%;
  width: 25px;
  height: 25px;
}

/* Enhanced Filter Modal */
.filter-modal {
  display: none;
  position: absolute;
  top: 60px;
  right: var(--spacing-4);
  z-index: 1001;
  background: var(--bg-primary);
  padding: var(--spacing-6);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-lg);
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(20px);
  min-width: 250px;
}

.filter-modal.active {
  display: block;
  animation: fadeInDown 0.3s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filter-modal h3 {
  margin-bottom: var(--spacing-4);
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  font-weight: 600;
}

.filter-modal div {
  margin-bottom: var(--spacing-3);
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: wrap;
}

.filter-modal button {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-2) var(--spacing-4);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  flex: 1;
  min-width: fit-content;
}

.filter-modal button:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-1px);
}

/* Enhanced Blog Section */
.headblog {
  text-align: center;
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin: var(--spacing-12) 0 var(--spacing-8) 0;
  position: relative;
}

.headblog::after {
  content: '';
  position: absolute;
  bottom: -var(--spacing-3);
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: 2px;
}

.blog21 {
  display: flex;
  padding: 0 var(--spacing-8);
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
  margin: var(--spacing-8) 0;
}

.wrapper {
  max-width: 1200px;
  width: 100%;
  position: relative;
  margin: 0 auto;
}

.wrapper i {
  top: 50%;
  height: 50px;
  width: 50px;
  cursor: pointer;
  font-size: 1.25rem;
  position: absolute;
  text-align: center;
  line-height: 50px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  box-shadow: var(--shadow-md);
  transform: translateY(-50%);
  transition: var(--transition);
  z-index: 10;
  color: var(--text-primary);
}

.wrapper i:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-50%) scale(1.1);
  box-shadow: var(--shadow-lg);
}

.wrapper i:active {
  transform: translateY(-50%) scale(0.95);
}

.wrapper i:first-child {
  left: -25px;
}

.wrapper i:last-child {
  right: -25px;
}
/* Enhanced Carousel */
.wrapper .carousel {
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: calc((100% / 3) - var(--spacing-4));
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  gap: var(--spacing-6);
  border-radius: var(--border-radius-lg);
  scroll-behavior: smooth;
  scrollbar-width: none;
  padding: var(--spacing-4) 0;
}

.carousel::-webkit-scrollbar {
  display: none;
}

.carousel.no-transition {
  scroll-behavior: auto;
}

.carousel.dragging {
  scroll-snap-type: none;
  scroll-behavior: auto;
}

.carousel.dragging .card {
  cursor: grab;
  user-select: none;
}

.carousel :where(.card, .img) {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Enhanced Blog Cards */
.carousel .card {
  scroll-snap-align: start;
  height: 350px;
  list-style: none;
  background: var(--bg-primary);
  cursor: pointer;
  flex-direction: column;
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-md);
  transition: var(--transition);
  overflow: hidden;
  position: relative;
}

.carousel .card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.carousel .card .img {
  height: 180px;
  width: 100%;
  overflow: hidden;
  position: relative;
}

.card .img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.carousel .card:hover .img img {
  transform: scale(1.05);
}

/* User Experience Cards */
.carousel .card #useimg {
  background: var(--bg-secondary);
  height: 120px;
  width: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid var(--border-color);
  margin: var(--spacing-4) 0;
}

.card #useimg img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

/* Card Content */
.carousel .card h2 {
  font-weight: 600;
  font-size: var(--font-size-xl);
  margin: var(--spacing-4) var(--spacing-4) var(--spacing-2);
  color: var(--text-primary);
  text-align: center;
}

.carousel .card p {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  line-height: 1.6;
  margin: 0 var(--spacing-4) var(--spacing-4);
  text-align: center;
}

.carousel .card #puser {
  font-style: italic;
  color: var(--text-secondary);
  padding: 0 var(--spacing-4);
}

/* Read More Button */
.read-more {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: var(--spacing-3) var(--spacing-5);
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 600;
  font-size: var(--font-size-sm);
  transition: var(--transition);
  margin: var(--spacing-4);
  box-shadow: var(--shadow-sm);
}

.read-more:hover {
  background: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* More Content Modal */
.more-content {
  display: none;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  background: var(--bg-primary);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-lg);
  border-radius: var(--border-radius-lg);
  z-index: 1000;
  overflow-y: auto;
  border: 1px solid var(--border-color);
}

.more-content .close {
  background: var(--danger-color);
  color: white;
  border: none;
  font-size: var(--font-size-lg);
  position: absolute;
  top: var(--spacing-4);
  right: var(--spacing-4);
  cursor: pointer;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.more-content .close:hover {
  background: #dc2626;
  transform: scale(1.1);
}

.overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
  backdrop-filter: blur(4px);
}

/* Responsive Carousel */
@media screen and (max-width: 900px) {
  .wrapper .carousel {
    grid-auto-columns: calc((100% / 2) - var(--spacing-3));
    gap: var(--spacing-4);
  }

  .wrapper i:first-child {
    left: -20px;
  }

  .wrapper i:last-child {
    right: -20px;
  }
}

@media screen and (max-width: 600px) {
  .wrapper .carousel {
    grid-auto-columns: 100%;
    gap: var(--spacing-3);
  }

  .carousel .card {
    height: 320px;
  }

  .blog21 {
    padding: 0 var(--spacing-4);
  }

  .wrapper i {
    display: none;
  }

  .headblog {
    font-size: var(--font-size-2xl);
    margin: var(--spacing-8) 0 var(--spacing-6) 0;
  }
}


li p {
  /* padding: 5px; */
    margin-bottom: 10px;
  }
  
  li .read-more {
    background-color: #007BFF;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
  }
  
  li .more-content {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: white;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    z-index: 100;
    overflow-y: auto;
  }
  
  li .more-content .close {
    background: none;
    border: none;
    font-size: 1.5em;
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
  }
  
  .overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 50;
  }

  .blog21{
    display: flex;
  padding: 0 35px;
  align-items: center;
  justify-content: center;
  height: 52.5vh;
  background: linear-gradient(to left top, #e6e8e9b2, #e6e8e9b2);
  }

  .blog21 h1{
    display: flex;
    justify-content: center;
    text-align: center;
    font-size: 2.5rem;
    color: #fff;
  }

  /* about us  */
  
.about-us {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.about-us h1 {
  text-align: center;
  font-style: normal;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 20px;
}

.about-us .content {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 20px;
}

.about-us .text {
  flex: 1 1 300px;
  min-width: 300px;
  padding: 20px;
}

.about-us .text h2 {
  font-size: 1.8rem;
  margin-bottom: 15px;
}

.about-us .text p {
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 10px;
  text-align: justify;
}

.about-us .image {
  flex: 1 1 300px;
  min-width: 300px;
}

.about-us .image img {
  width: 100%;
  height: auto;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(190, 82, 82, 0.1);
}


@media (max-width: 768px) {
  .about-us .content {
    flex-direction: column;
  }
}

.about-us .text .read-more {
  background-color: #007BFF;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
}

h1{
  text-align: center;
  font-style: normal;
  font-size: 1.5rem;
  font-weight: 600;
}

#puser{
  padding: 0 20px 20px 20px;
  text-align: justify;
}

.footer-bottom {
  font-size: smaller;
  font-style: italic;
  font-weight: 400;
  color: #dbd9d98f;
  text-align: center;
  /* margin-top: 20px; */
  border-top: 1px solid #444;
  padding: 10px;
  background-color: #000;
}