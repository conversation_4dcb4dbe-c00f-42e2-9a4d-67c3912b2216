{"program": {"fileNames": ["../../../node_modules/typescript/lib/lib.es5.d.ts", "../../../node_modules/typescript/lib/lib.es2015.d.ts", "../../../node_modules/typescript/lib/lib.es2016.d.ts", "../../../node_modules/typescript/lib/lib.es2017.d.ts", "../../../node_modules/typescript/lib/lib.es2018.d.ts", "../../../node_modules/typescript/lib/lib.dom.d.ts", "../../../node_modules/typescript/lib/lib.es2015.core.d.ts", "../../../node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../../node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../../node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../../node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../../node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../../node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../../node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../../node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../../node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../../node_modules/typescript/lib/lib.es2017.object.d.ts", "../../../node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../../node_modules/typescript/lib/lib.es2017.string.d.ts", "../../../node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../../node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../../node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../../node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../../node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../../node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../../node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../../node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../../node_modules/typescript/lib/lib.esnext.intl.d.ts", "../src/index.ts", "../../../node_modules/@babel/types/lib/index.d.ts", "../../../node_modules/@types/babel__generator/index.d.ts", "../../../node_modules/@types/babel__traverse/index.d.ts", "../../../node_modules/@babel/parser/typings/babel-parser.d.ts", "../../../node_modules/@types/babel__template/index.d.ts", "../../../node_modules/@types/babel__core/index.d.ts", "../../../node_modules/@types/color-name/index.d.ts", "../../../node_modules/@types/node/globals.d.ts", "../../../node_modules/@types/node/async_hooks.d.ts", "../../../node_modules/@types/node/buffer.d.ts", "../../../node_modules/@types/node/child_process.d.ts", "../../../node_modules/@types/node/cluster.d.ts", "../../../node_modules/@types/node/console.d.ts", "../../../node_modules/@types/node/constants.d.ts", "../../../node_modules/@types/node/crypto.d.ts", "../../../node_modules/@types/node/dgram.d.ts", "../../../node_modules/@types/node/dns.d.ts", "../../../node_modules/@types/node/domain.d.ts", "../../../node_modules/@types/node/events.d.ts", "../../../node_modules/@types/node/fs.d.ts", "../../../node_modules/@types/node/fs/promises.d.ts", "../../../node_modules/@types/node/http.d.ts", "../../../node_modules/@types/node/http2.d.ts", "../../../node_modules/@types/node/https.d.ts", "../../../node_modules/@types/node/inspector.d.ts", "../../../node_modules/@types/node/module.d.ts", "../../../node_modules/@types/node/net.d.ts", "../../../node_modules/@types/node/os.d.ts", "../../../node_modules/@types/node/path.d.ts", "../../../node_modules/@types/node/perf_hooks.d.ts", "../../../node_modules/@types/node/process.d.ts", "../../../node_modules/@types/node/punycode.d.ts", "../../../node_modules/@types/node/querystring.d.ts", "../../../node_modules/@types/node/readline.d.ts", "../../../node_modules/@types/node/repl.d.ts", "../../../node_modules/@types/node/stream.d.ts", "../../../node_modules/@types/node/string_decoder.d.ts", "../../../node_modules/@types/node/timers.d.ts", "../../../node_modules/@types/node/tls.d.ts", "../../../node_modules/@types/node/trace_events.d.ts", "../../../node_modules/@types/node/tty.d.ts", "../../../node_modules/@types/node/url.d.ts", "../../../node_modules/@types/node/util.d.ts", "../../../node_modules/@types/node/v8.d.ts", "../../../node_modules/@types/node/vm.d.ts", "../../../node_modules/@types/node/worker_threads.d.ts", "../../../node_modules/@types/node/zlib.d.ts", "../../../node_modules/@types/node/base.d.ts", "../../../node_modules/@types/node/ts3.2/fs.d.ts", "../../../node_modules/@types/node/ts3.2/util.d.ts", "../../../node_modules/@types/node/ts3.2/globals.d.ts", "../../../node_modules/@types/node/ts3.2/base.d.ts", "../../../node_modules/@types/node/ts3.5/globals.global.d.ts", "../../../node_modules/@types/node/ts3.5/wasi.d.ts", "../../../node_modules/@types/node/ts3.5/base.d.ts", "../../../node_modules/@types/node/ts3.7/assert.d.ts", "../../../node_modules/@types/node/ts3.7/base.d.ts", "../../../node_modules/@types/node/ts3.7/index.d.ts", "../../../node_modules/@types/graceful-fs/index.d.ts", "../../../node_modules/@types/istanbul-lib-coverage/index.d.ts", "../../../node_modules/@types/istanbul-lib-report/index.d.ts", "../../../node_modules/@types/istanbul-reports/index.d.ts", "../../../node_modules/@types/normalize-package-data/index.d.ts", "../../../node_modules/@types/prettier/index.d.ts", "../../../node_modules/@types/stack-utils/index.d.ts", "../../../node_modules/@types/yargs-parser/index.d.ts", "../../../node_modules/@types/yargs/index.d.ts"], "fileInfos": [{"version": "89f78430e422a0f06d13019d60d5a45b37ec2d28e67eb647f73b1b0d19a46b72", "affectsGlobalScope": true}, "dc47c4fa66b9b9890cf076304de2a9c5201e94b740cffdf09f87296d877d71f6", "7a387c58583dfca701b6c85e0adaf43fb17d590fb16d5b2dc0a2fbd89f35c467", "8a12173c586e95f4433e0c6dc446bc88346be73ffe9ca6eec7aa63c8f3dca7f9", "5f4e733ced4e129482ae2186aae29fde948ab7182844c3a5a51dd346182c7b06", {"version": "abba1071bfd89e55e88a054b0c851ea3e8a494c340d0f3fab19eb18f6afb0c9e", "affectsGlobalScope": true}, {"version": "d8996609230d17e90484a2dd58f22668f9a05a3bfe00bfb1d6271171e54a31fb", "affectsGlobalScope": true}, {"version": "43fb1d932e4966a39a41b464a12a81899d9ae5f2c829063f5571b6b87e6d2f9c", "affectsGlobalScope": true}, {"version": "cdccba9a388c2ee3fd6ad4018c640a471a6c060e96f1232062223063b0a5ac6a", "affectsGlobalScope": true}, {"version": "4378fc8122ec9d1a685b01eb66c46f62aba6b239ca7228bb6483bcf8259ee493", "affectsGlobalScope": true}, {"version": "0d5f52b3174bee6edb81260ebcd792692c32c81fd55499d69531496f3f2b25e7", "affectsGlobalScope": true}, {"version": "810627a82ac06fb5166da5ada4159c4ec11978dfbb0805fe804c86406dab8357", "affectsGlobalScope": true}, {"version": "62d80405c46c3f4c527ee657ae9d43fda65a0bf582292429aea1e69144a522a6", "affectsGlobalScope": true}, {"version": "3013574108c36fd3aaca79764002b3717da09725a36a6fc02eac386593110f93", "affectsGlobalScope": true}, {"version": "75ec0bdd727d887f1b79ed6619412ea72ba3c81d92d0787ccb64bab18d261f14", "affectsGlobalScope": true}, {"version": "3be5a1453daa63e031d266bf342f3943603873d890ab8b9ada95e22389389006", "affectsGlobalScope": true}, {"version": "17bb1fc99591b00515502d264fa55dc8370c45c5298f4a5c2083557dccba5a2a", "affectsGlobalScope": true}, {"version": "7ce9f0bde3307ca1f944119f6365f2d776d281a393b576a18a2f2893a2d75c98", "affectsGlobalScope": true}, {"version": "6a6b173e739a6a99629a8594bfb294cc7329bfb7b227f12e1f7c11bc163b8577", "affectsGlobalScope": true}, {"version": "12a310447c5d23c7d0d5ca2af606e3bd08afda69100166730ab92c62999ebb9d", "affectsGlobalScope": true}, {"version": "b0124885ef82641903d232172577f2ceb5d3e60aed4da1153bab4221e1f6dd4e", "affectsGlobalScope": true}, {"version": "0eb85d6c590b0d577919a79e0084fa1744c1beba6fd0d4e951432fa1ede5510a", "affectsGlobalScope": true}, {"version": "da233fc1c8a377ba9e0bed690a73c290d843c2c3d23a7bd7ec5cd3d7d73ba1e0", "affectsGlobalScope": true}, {"version": "d154ea5bb7f7f9001ed9153e876b2d5b8f5c2bb9ec02b3ae0d239ec769f1f2ae", "affectsGlobalScope": true}, {"version": "bb2d3fb05a1d2ffbca947cc7cbc95d23e1d053d6595391bd325deb265a18d36c", "affectsGlobalScope": true}, {"version": "c80df75850fea5caa2afe43b9949338ce4e2de086f91713e9af1a06f973872b8", "affectsGlobalScope": true}, {"version": "1b3fe904465430e030c93239a348f05e1be80640d91f2f004c3512c2c2c89f34", "affectsGlobalScope": true}, {"version": "10bbdc1981b8d9310ee75bfac28ee0477bb2353e8529da8cff7cb26c409cb5e8", "affectsGlobalScope": true}, "54bf1add73651b7392172bd0dedd9a227f2008cb5d0c851b661ecfae9fc0069d", "59706a76a49e1a9ed521cf6e1a1fd1496be0b5cddcaf6be71837cfe39017b0d6", "b25c5f2970d06c729f464c0aeaa64b1a5b5f1355aa93554bb5f9c199b8624b1e", "0b27e24be20d2385a284637c0b202678813e3322e9ac73016f8f10e621bca881", "56dede90729ef85351f132d66e05510851211d627e2f2c6621475d4f7938f365", "3e0a34f7207431d967dc32d593d1cda0c23975e9484bc8895b39d96ffca4a0d8", "af523cd24a11e1feadface05a9ac4f692a99bcb65c020b96061c63ac593824ad", "f0cb4b3ab88193e3e51e9e2622e4c375955003f1f81239d72c5b7a95415dad3e", {"version": "4e69904af7675b7028b88a8ce1edce9725f33d1ae7b0776468cf5f014f7dc017", "affectsGlobalScope": true}, "4ed9f71ddbb5753771ee391f64297078a88f7dfd1480646dcf08c31395778682", "61215c1a376bbe8f51cab4cc4ddbf3746387015113c37a84d981d4738c21b878", "fa943836191d443840b5fc1ba1450a706c83ca437de44d5fee2a5c9a9281ed0a", "123ec69e4b3a686eb49afd94ebe3292a5c84a867ecbcb6bb84bdd720a12af803", "525c8fc510d9632d2a0a9de2d41c3ac1cdd79ff44d3b45c6d81cacabb683528d", "90c85ddbb8de82cd19198bda062065fc51b7407c0f206f2e399e65a52e979720", "d4dd0b19ee0338dd4f1603eacb41859b9d5371bfef2b2849cb870d6fd6602bcb", "7ecfe97b43aa6c8b8f90caa599d5648bb559962e74e6f038f73a77320569dd78", "aad3237c3f99480041cad7ca04d64307c98933996f822342b7c0ee4a78553346", "4d4c83f77ac21a72252785baa5328a5612b0b6598d512f68b8cb14f7966d059e", "eaa8136bb11fbea5bdaf29e06aa45a1969ddd39fbfb5fe58a01f00d7f1562cd9", "e253cd3c7d10c4f600308d0528dd371d7e4165d8295b37a1f38d0ef6c0dfaf60", "fb28748ff8d015f52e99daee4f454e57cec1a22141f1257c317f3630a15edeb7", "b5fd0a137bd6d0afe291d465e99c7469b082b66b3ee89273b3b22801b6c2948e", "2927d4b5a6df18d3cbf35e30b44dd2cc7f45c74fb8978594192c33bc1dd584c0", "c969bf4c7cdfe4d5dd28aa09432f99d09ad1d8d8b839959646579521d0467d1a", "6c3857edaeeaaf43812f527830ebeece9266b6e8eb5271ab6d2f0008306c9947", "bc6a77e750f4d34584e46b1405b771fb69a224197dd6bafe5b0392a29a70b665", "46cac76114704902baa535b30fb66a26aeaf9430f3b3ab44746e329f12e85498", "ed4ae81196cccc10f297d228bca8d02e31058e6d723a3c5bc4be5fb3c61c6a34", "84044697c8b3e08ef24e4b32cfe6440143d07e469a5e34bda0635276d32d9f35", "0b6098fedb648cab8091cca2b022a5c729b6ef18da923852033f495907cb1a45", {"version": "0e0d58f5e90c0a270dac052b9c5ad8ccdfc8271118c2105b361063218d528d6e", "affectsGlobalScope": true}, "30ec6f9c683b988c3cfaa0c4690692049c4e7ed7dc6f6e94f56194c06b86f5e1", "9f633ecf3e065ff82c19eccab35c8aa1d6d5d1a49af282dc29ef5a64cca34164", "6b2bb67b0942bcfce93e1d6fad5f70afd54940a2b13df7f311201fba54b2cbe9", "dd3706b25d06fe23c73d16079e8c66ac775831ef419da00716bf2aee530a04a4", "bc63cc31a7a7b4117e0462fbdb7a08aab5d59b4b8c8f464fe4c984977ec462d4", "d67e08745494b000da9410c1ae2fdc9965fc6d593fe0f381a47491f75417d457", "b40652bf8ce4a18133b31349086523b219724dca8df3448c1a0742528e7ad5b9", "424bc64b2794d9280c1e1f4a3518ba9d285385a16d84753a6427bb469e582eca", "a77fdb357c78b70142b2fdbbfb72958d69e8f765fd2a3c69946c1018e89d4638", "3c2ac350c3baa61fd2b1925844109e098f4376d0768a4643abc82754fd752748", "826d48e49c905cedb906cbde6ccaf758827ff5867d4daa006b5a79e0fb489357", "baa711b17f67390c60eac3c70a1391b23a8e3833cb723b2d7336d4817a22455c", "289be113bad7ee27ee7fa5b1e373c964c9789a5e9ed7db5ddcb631371120b953", "e4abb8eaa8a7d78236be0f8342404aab076668d20590209e32fdeb924588531e", "086bfc0710b044ce1586108ee56c6e1c0d9ca2d325c153bb026cbc850169f593", "f409183966a1dd93d3a9cd1d54fbeb85c73101e87cd5b19467c5e37b252f3fd8", "92d6395892ec8da8c51def5a12b012fa63309d06b4933a35ced5c732bda5ca11", "12b2608d6074167c331c9c3c6994a57819f6ff934c7fd4527e23aabf56d4c8d1", "ffc1cd688606ad1ddb59a40e8f3defbde907af2a3402d1d9ddf69accb2903f07", {"version": "4926e99d2ad39c0bbd36f2d37cc8f52756bc7a5661ad7b12815df871a4b07ba1", "affectsGlobalScope": true}, "4cef33b2997388559c39b2f98c37e8319ad61e30a1f0edc55c53913f2250bade", {"version": "2708349d5a11a5c2e5f3a0765259ebe7ee00cdcc8161cb9990cb4910328442a1", "affectsGlobalScope": true}, "0b3fef11ea6208c4cb3715c9aa108766ce98fc726bfba68cc23b25ce944ce9c0", "255dbc5a5acef2b83b47145042aa0127ebf7fe24cd5ce6afaaaf5c8fc2c5eb96", "a8b842671d535d14f533fd8dbfacebceacf5195069d720425d572d5cc5ab3dc4", "9779312cffccce68e3ffbaa3a876381dc54a8240d9bdaa448f7eba222ec19392", "d522314e80ed71b57e3c2939d3c9594eaae63a4adf028559e6574f6b270b0fee", "2c7dca525f4e2e5f2b357dacb58ab6c8777995e6d505ef652bcbbf9789ac558f", "b7b0f360867e85305ac700e4c6f084d45adfa417a8f5d964b6f609f99ee92772", "7eb06594824ada538b1d8b48c3925a83e7db792f47a081a62cf3e5c4e23cf0ee", "029769d13d9917e3284cb2356ed28a6576e8b07ae6a06ee1e672518adf21a102", "c9ad058b2cc9ce6dc2ed92960d6d009e8c04bef46d3f5312283debca6869f613", "9f648f662bddda1b68b01992edd275c214838124a43c8d9210e4a61b974ff82d", "41422586881bcd739b4e62d9b91cd29909f8572aa3e3cdf316b7c50f14708d49", "fdfbe321c556c39a2ecf791d537b999591d0849e971dd938d88f460fea0186f6", "27d92f477d76685eff84f9cc352f8a56d024b6bfdac426e37c2c5ece37c4d733"], "options": {"composite": true, "declaration": true, "module": 1, "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": false, "outDir": "./", "rootDir": "../src", "sourceMap": true, "strict": true, "target": 1, "tsBuildInfoFile": "./tsconfig.tsbuildinfo"}, "fileIdsList": [[30, 49, 72, 78, 79, 88], [49, 72, 78, 79, 88], [30, 31, 32, 33, 34, 49, 72, 78, 79, 88], [30, 33, 49, 72, 78, 79, 88], [49, 72, 78, 79, 87, 88], [49, 72, 78, 79, 88, 89], [49, 72, 78, 79, 88, 89, 90], [37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 78, 79, 88], [48, 49, 56, 65, 72, 78, 79, 88], [40, 48, 49, 56, 72, 78, 79, 88], [44, 49, 57, 72, 78, 79, 88], [49, 65, 72, 78, 79, 88], [46, 48, 49, 56, 72, 78, 79, 88], [48, 49, 72, 78, 79, 88], [48, 50, 65, 71, 72, 78, 79, 88], [48, 49, 56, 65, 71, 72, 78, 79, 88], [48, 49, 51, 56, 65, 68, 71, 72, 78, 79, 88], [48, 49, 51, 68, 71, 72, 78, 79, 88], [49, 71, 72, 78, 79, 88], [46, 48, 49, 65, 72, 78, 79, 88], [38, 49, 72, 78, 79, 88], [49, 70, 72, 78, 79, 88], [48, 49, 65, 72, 78, 79, 88], [49, 63, 72, 74, 78, 79, 88], [44, 46, 49, 56, 65, 72, 78, 79, 88], [49, 72, 77, 78, 79, 80, 88], [49, 72, 79, 88], [37, 49, 72, 78, 79, 88], [49, 72, 78, 88], [49, 72, 78, 79, 81, 82, 83, 88], [49, 72, 78, 79, 84, 85, 88], [49, 72, 78, 79, 86, 88], [49, 56, 72, 78, 79, 88], [49, 62, 72, 78, 79, 88], [49, 78, 79, 88], [48, 49, 65, 71, 72, 74, 78, 79, 88], [49, 72, 78, 79, 88, 95]], "referencedMap": [[33, 1], [30, 2], [35, 3], [31, 1], [34, 4], [32, 1], [36, 2], [88, 5], [89, 2], [90, 6], [91, 7], [38, 2], [77, 8], [39, 2], [40, 9], [41, 10], [42, 2], [43, 11], [44, 12], [45, 13], [46, 2], [47, 14], [48, 2], [49, 15], [50, 2], [37, 2], [51, 16], [52, 17], [53, 18], [54, 14], [55, 19], [56, 20], [57, 2], [58, 2], [59, 21], [60, 22], [61, 2], [62, 2], [63, 23], [64, 24], [65, 14], [66, 2], [67, 2], [68, 25], [69, 2], [81, 26], [78, 27], [80, 28], [79, 29], [84, 30], [82, 2], [83, 2], [85, 2], [86, 31], [87, 32], [70, 33], [71, 34], [72, 35], [73, 12], [74, 2], [75, 36], [76, 12], [92, 2], [93, 2], [94, 2], [95, 2], [96, 37], [6, 2], [8, 2], [7, 2], [2, 2], [9, 2], [10, 2], [11, 2], [12, 2], [13, 2], [14, 2], [15, 2], [16, 2], [3, 2], [4, 2], [20, 2], [17, 2], [18, 2], [19, 2], [21, 2], [22, 2], [23, 2], [5, 2], [24, 2], [25, 2], [26, 2], [27, 2], [1, 2], [28, 2], [29, 2]], "exportedModulesMap": [[33, 1], [30, 2], [35, 3], [31, 1], [34, 4], [32, 1], [36, 2], [88, 5], [89, 2], [90, 6], [91, 7], [38, 2], [77, 8], [39, 2], [40, 9], [41, 10], [42, 2], [43, 11], [44, 12], [45, 13], [46, 2], [47, 14], [48, 2], [49, 15], [50, 2], [37, 2], [51, 16], [52, 17], [53, 18], [54, 14], [55, 19], [56, 20], [57, 2], [58, 2], [59, 21], [60, 22], [61, 2], [62, 2], [63, 23], [64, 24], [65, 14], [66, 2], [67, 2], [68, 25], [69, 2], [81, 26], [78, 27], [80, 28], [79, 29], [84, 30], [82, 2], [83, 2], [85, 2], [86, 31], [87, 32], [70, 33], [71, 34], [72, 35], [73, 12], [74, 2], [75, 36], [76, 12], [92, 2], [93, 2], [94, 2], [95, 2], [96, 37], [6, 2], [8, 2], [7, 2], [2, 2], [9, 2], [10, 2], [11, 2], [12, 2], [13, 2], [14, 2], [15, 2], [16, 2], [3, 2], [4, 2], [20, 2], [17, 2], [18, 2], [19, 2], [21, 2], [22, 2], [23, 2], [5, 2], [24, 2], [25, 2], [26, 2], [27, 2], [1, 2], [28, 2], [29, 2]], "semanticDiagnosticsPerFile": [33, 30, 35, 31, 34, 32, 36, 88, 89, 90, 91, 38, 77, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 37, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 81, 78, 80, 79, 84, 82, 83, 85, 86, 87, 70, 71, 72, 73, 74, 75, 76, 92, 93, 94, 95, 96, 6, 8, 7, 2, 9, 10, 11, 12, 13, 14, 15, 16, 3, 4, 20, 17, 18, 19, 21, 22, 23, 5, 24, 25, 26, 27, 1, 28, 29]}, "version": "4.5.4"}