{"name": "pug", "description": "A clean, whitespace-sensitive template language for writing HTML", "keywords": ["html", "jade", "pug", "template"], "version": "3.0.3", "author": "<PERSON><PERSON> <<EMAIL>>", "maintainers": ["Forbes Lindesay <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <jona<PERSON><PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <*************>"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pugjs/pug/tree/master/packages/pug"}, "main": "lib", "dependencies": {"pug-code-gen": "^3.0.3", "pug-filters": "^4.0.0", "pug-lexer": "^5.0.1", "pug-linker": "^4.0.0", "pug-load": "^3.0.0", "pug-parser": "^6.0.0", "pug-runtime": "^3.0.1", "pug-strip-comments": "^2.0.0"}, "devDependencies": {"jstransformer-cdata": "^1.0.0", "jstransformer-coffee-script": "^1.0.0", "jstransformer-less": "^2.1.0", "jstransformer-markdown-it": "^2.0.0", "jstransformer-stylus": "^1.0.0", "jstransformer-uglify-js": "^1.1.1", "jstransformer-verbatim": "^1.0.0", "mkdirp": "^0.5.1", "rimraf": "^3.0.2", "uglify-js": "github:mishoo/UglifyJS2#1c15d0db456ce32f1b9b507aad97e5ee5c8285f7"}, "files": ["lib/index.js", "register.js"], "browser": {"fs": false}, "homepage": "https://pugjs.org"}