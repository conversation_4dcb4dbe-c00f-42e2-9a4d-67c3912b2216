{"name": "to-fast-properties", "version": "2.0.0", "description": "Force V8 to use fast properties for an object", "license": "MIT", "repository": "sindresorhus/to-fast-properties", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "node --allow-natives-syntax test.js"}, "files": ["index.js"], "keywords": ["object", "obj", "properties", "props", "v8", "optimize", "fast", "convert", "mode"], "devDependencies": {"ava": "0.0.4"}}