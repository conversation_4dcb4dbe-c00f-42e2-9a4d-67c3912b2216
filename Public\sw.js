/**
 * Service Worker for EV Station Finder
 * Provides offline functionality and caching
 */

const CACHE_NAME = 'ev-station-finder-v1';
const urlsToCache = [
  '/',
  '/css/style.css',
  '/css/style1.css',
  '/css/enhanced-components.css',
  '/js/script.js',
  '/js/script1.js',
  '/js/enhanced-ui.js',
  '/imgs/20240602_081559564_iOS.png',
  'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css',
  'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js',
  'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap'
];

// Install event - cache resources
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Opened cache');
        return cache.addAll(urlsToCache);
      })
  );
});

// Fetch event - serve from cache when offline
self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Return cached version or fetch from network
        if (response) {
          return response;
        }
        return fetch(event.request);
      }
    )
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

// Background sync for offline form submissions
self.addEventListener('sync', (event) => {
  if (event.tag === 'contact-form-sync') {
    event.waitUntil(syncContactForm());
  }
});

async function syncContactForm() {
  // Handle offline form submissions when back online
  const formData = await getStoredFormData();
  if (formData) {
    try {
      await fetch('/sendContactUsDetails', {
        method: 'POST',
        body: formData
      });
      await clearStoredFormData();
    } catch (error) {
      console.error('Failed to sync form data:', error);
    }
  }
}

async function getStoredFormData() {
  // Implementation would depend on IndexedDB or localStorage
  return null;
}

async function clearStoredFormData() {
  // Clear stored form data after successful sync
}
