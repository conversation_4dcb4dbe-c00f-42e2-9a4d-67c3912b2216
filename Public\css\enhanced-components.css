/**
 * Enhanced UI Components CSS
 * Modern styling for improved user experience
 */

/* Animation Classes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Animation Utilities */
.animate-in {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-in-left {
  animation: fadeInLeft 0.6s ease-out forwards;
}

.animate-in-right {
  animation: fadeInRight 0.6s ease-out forwards;
}

.pulse {
  animation: pulse 2s infinite;
}

/* Loading Spinner */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
  margin-right: var(--spacing-2);
}

/* Enhanced Navigation Active States */
nav a.active {
  color: var(--primary-color);
  background: rgba(37, 99, 235, 0.1);
  position: relative;
}

nav a.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background: var(--primary-color);
  border-radius: 1px;
}

/* Enhanced Form Styling */
.input-group {
  position: relative;
  margin-bottom: var(--spacing-5);
}

.input-group input,
.input-group textarea {
  width: 100%;
  padding: var(--spacing-4);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  transition: var(--transition);
  background: var(--bg-primary);
}

.input-group input:focus,
.input-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.input-group input.error,
.input-group textarea.error {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input-group label {
  position: absolute;
  top: var(--spacing-4);
  left: var(--spacing-4);
  color: var(--text-secondary);
  font-size: var(--font-size-base);
  transition: var(--transition);
  pointer-events: none;
  background: var(--bg-primary);
  padding: 0 var(--spacing-2);
}

.input-group input:focus + label,
.input-group input:not(:placeholder-shown) + label,
.input-group textarea:focus + label,
.input-group textarea:not(:placeholder-shown) + label {
  top: -8px;
  font-size: var(--font-size-sm);
  color: var(--primary-color);
}

.field-error {
  color: var(--danger-color);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-1);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.field-error::before {
  content: '⚠';
  font-size: var(--font-size-xs);
}

/* Enhanced Tooltips */
.tooltip {
  position: absolute;
  background: var(--bg-dark);
  color: white;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  white-space: nowrap;
  z-index: 1200;
  opacity: 0;
  transform: translateY(5px);
  transition: var(--transition);
  pointer-events: none;
}

.tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: var(--bg-dark);
}

.tooltip.show {
  opacity: 1;
  transform: translateY(0);
}

/* Notification System */
.notification-container {
  position: fixed;
  top: var(--spacing-4);
  right: var(--spacing-4);
  z-index: 1300;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  max-width: 400px;
}

.notification {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-4);
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transform: translateX(100%);
  transition: var(--transition);
  backdrop-filter: blur(10px);
}

.notification.show {
  transform: translateX(0);
}

.notification.hide {
  transform: translateX(100%);
  opacity: 0;
}

.notification-info {
  border-left: 4px solid var(--primary-color);
}

.notification-success {
  border-left: 4px solid var(--success-color);
}

.notification-warning {
  border-left: 4px solid var(--warning-color);
}

.notification-error {
  border-left: 4px solid var(--danger-color);
}

.notification-message {
  flex: 1;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
}

.notification-close {
  background: none;
  border: none;
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  margin-left: var(--spacing-3);
  transition: var(--transition);
}

.notification-close:hover {
  color: var(--text-primary);
  transform: scale(1.1);
}

/* Enhanced Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-5);
  border: none;
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: var(--font-size-sm);
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--secondary-color);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--secondary-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

/* Enhanced Card Hover Effects */
.card-hover {
  transition: var(--transition);
}

.card-hover:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-lg);
}

/* Responsive Utilities */
@media (max-width: 768px) {
  .notification-container {
    left: var(--spacing-4);
    right: var(--spacing-4);
    max-width: none;
  }
  
  .tooltip {
    font-size: var(--font-size-xs);
    padding: var(--spacing-1) var(--spacing-2);
  }
}
