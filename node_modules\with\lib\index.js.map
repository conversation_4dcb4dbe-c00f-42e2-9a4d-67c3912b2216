{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,0CAAoC;AACpC,2CAA6C;AAC7C,gDAAkC;AAClC,wDAA+B;AAE/B,MAAM,YAAY,GAAG;IACnB,0BAA0B,EAAE,IAAI;IAChC,2BAA2B,EAAE,IAAI;CAClC,CAAC;AAEF;;;;;;GAMG;AACH,SAAwB,OAAO,CAC7B,GAAW,EACX,GAAW,EACX,UAAoB,EAAE;IAEtB,sDAAsD;IACtD,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;IACf,sDAAsD;IACtD,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;IAEf,IAAI,GAAG,CAAC;IACR,IAAI;QACF,GAAG,GAAG,cAAK,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;KAChC;IAAC,OAAO,CAAC,EAAE;QACV,MAAM,MAAM,CAAC,MAAM,CACjB,IAAI,KAAK,CAAC,2CAA2C,CAAC,EACtD;YACE,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,CAAC;SAChB,CACF,CAAC;KACH;IACD,IAAI,MAAM,CAAC;IACX,IAAI;QACF,MAAM,GAAG,cAAK,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;KACnC;IAAC,OAAO,CAAC,EAAE;QACV,MAAM,MAAM,CAAC,MAAM,CACjB,IAAI,KAAK,CAAC,kDAAkD,CAAC,EAC7D;YACE,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,CAAC;SAChB,CACF,CAAC;KACH;IACD,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC;QACzB,WAAW;QACX,MAAM;QACN,GAAG,OAAO;QACV,GAAG,iBAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;KACrC,CAAC,CAAC;IAEH,MAAM,IAAI,GAAG,IAAI,GAAG,CAClB,iBAAM,CAAC,GAAG,CAAC;SACR,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;SAC5B,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACrC,CAAC;IAEF,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC;QAAE,OAAO,GAAG,CAAC;IAEhC,IAAI,YAAY,GAAG,EAAE,CAAC;IACtB,IAAI,KAAK,GAAG,iBAAiB,CAAC;IAC9B,IAAI,MAAM,GAAG,gBAAgB,CAAC;IAC9B,IAAI,CAAC,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE;QAC5B,KAAK,GAAG,GAAG,CAAC;KACb;SAAM;QACL,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAC/C,KAAK,IAAI,GAAG,CAAC;SACd;QACD,YAAY,GAAG,OAAO,KAAK,OAAO,GAAG,IAAI,CAAC;KAC3C;IACD,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;QACjD,MAAM,IAAI,GAAG,CAAC;KACf;IAED,MAAM,IAAI,GAAG;QACX,MAAM;QACN,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CACrB,CAAC,CAAC,EAAE,EAAE,CACJ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,KAAK;UAC9B,KAAK,IAAI,CAAC;iBACH,CAAC,sBAAsB,CAAC,cAAc,CAClD;KACF,CAAC;IAEF,MAAM,SAAS,GAAG,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;IAElD,OAAO;MACH,YAAY;MACZ,SAAS,CAAC,MAAM;iBACL,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QACpC,SAAS,CAAC,IAAI;aACT,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;MACtB,SAAS,CAAC,KAAK,GAAG,CAAC;AACzB,CAAC;AAnFD,0BAmFC;AAOD,MAAM,qBAAqB,GAAG,sBAAI,CAAqB;IACrD,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;QACxB,gDAAgD;IAClD,CAAC;IAED,eAAe,CAAC,IAAI,EAAE,KAAK;QACzB,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;QACvB,IAAI,KAAK,GAAG,EAAE,CAAC;QACf,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,KAAK,GAAG,WAAW,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;SACnD;QACD,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,WAAW,KAAK,IAAI,CAAC,CAAC;IAC5C,CAAC;CACF,CAAC,CAAC;AAEH;;;;;;GAMG;AACH,SAAS,aAAa,CAAC,GAAW,EAAE,GAAW,EAAE,MAAc;IAC7D,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IAEhC,MAAM,KAAK,GAAuB;QAChC,SAAS,EAAE,KAAK;QAChB,MAAM,CAAC,IAAI;YACT,OAAO,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,KAAM,EAAE,IAAI,CAAC,GAAI,CAAC,CAAC;QAC3C,CAAC;QACD,OAAO,CAAC,IAAI,EAAE,GAAG;YACf,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAM,EAAE,IAAI,CAAC,GAAI,CAAC,CAAC;YAC3C,SAAS,CAAC,IAAI,CAAC,KAAM,CAAC,GAAG,GAAG,CAAC;QAC/B,CAAC;KACF,CAAC;IAEF,qBAAqB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAElC,OAAO;QACL,MAAM,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,MAAM,KAAK,CAAC,CAAC,CAAC,EAAE;QACjD,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;QACxB,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,MAAM,YAAY,MAAM,QAAQ,CAAC,CAAC,CAAC,EAAE;KACvE,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;AACzB,MAAM,CAAC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC"}